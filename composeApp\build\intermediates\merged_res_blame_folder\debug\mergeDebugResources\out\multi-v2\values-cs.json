{"logs": [{"outputFile": "com.mediatek.gpu.governor.composeApp-mergeDebugResources-45:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c89a420d9a9a1fa6ac16fec2566676f\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "194,292,394,495,594,699,806,8486", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "287,389,490,589,694,801,920,8582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1115e786e037742d4d588599683945b9\\transformed\\ui-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,288,371,465,567,659,737,829,920,1001,1083,1169,1241,1330,1408,1484,1559,1638,1706", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,88,77,75,74,78,67,119", "endOffsets": "283,366,460,562,654,732,824,915,996,1078,1164,1236,1325,1403,1479,1554,1633,1701,1821"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1018,1101,1195,1297,1389,1467,7756,7847,7928,8010,8096,8168,8257,8335,8411,8587,8666,8734", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,88,77,75,74,78,67,119", "endOffsets": "1013,1096,1190,1292,1384,1462,1554,7842,7923,8005,8091,8163,8252,8330,8406,8481,8661,8729,8849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99e5d1610f557242616bbc837e13481b\\transformed\\foundation-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,230", "endColumns": "88,85,88", "endOffsets": "139,225,314"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8854,8940", "endColumns": "88,85,88", "endOffsets": "189,8935,9024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9363d7fb6059d76c439085d85c29cdfb\\transformed\\material3-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,409,527,628,723,835,969,1085,1224,1309,1409,1502,1599,1715,1837,1942,2075,2205,2347,2510,2638,2755,2879,3000,3091,3188,3308,3423,3521,3624,3732,3864,4005,4115,4214,4298,4392,4487,4599,4691,4777,4890,4970,5056,5157,5260,5357,5458,5546,5652,5751,5854,5973,6053,6157", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "168,285,404,522,623,718,830,964,1080,1219,1304,1404,1497,1594,1710,1832,1937,2070,2200,2342,2505,2633,2750,2874,2995,3086,3183,3303,3418,3516,3619,3727,3859,4000,4110,4209,4293,4387,4482,4594,4686,4772,4885,4965,5051,5152,5255,5352,5453,5541,5647,5746,5849,5968,6048,6152,6247"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1559,1677,1794,1913,2031,2132,2227,2339,2473,2589,2728,2813,2913,3006,3103,3219,3341,3446,3579,3709,3851,4014,4142,4259,4383,4504,4595,4692,4812,4927,5025,5128,5236,5368,5509,5619,5718,5802,5896,5991,6103,6195,6281,6394,6474,6560,6661,6764,6861,6962,7050,7156,7255,7358,7477,7557,7661", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "1672,1789,1908,2026,2127,2222,2334,2468,2584,2723,2808,2908,3001,3098,3214,3336,3441,3574,3704,3846,4009,4137,4254,4378,4499,4590,4687,4807,4922,5020,5123,5231,5363,5504,5614,5713,5797,5891,5986,6098,6190,6276,6389,6469,6555,6656,6759,6856,6957,7045,7151,7250,7353,7472,7552,7656,7751"}}]}]}