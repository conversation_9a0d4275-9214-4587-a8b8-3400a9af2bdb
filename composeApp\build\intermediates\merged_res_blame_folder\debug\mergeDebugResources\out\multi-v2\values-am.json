{"logs": [{"outputFile": "com.mediatek.gpu.governor.composeApp-mergeDebugResources-45:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1115e786e037742d4d588599683945b9\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "178,261,338,430,526,608,686,769,851,929,1007,1088,1158,1241,1314,1387,1459,1539,1604", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,82,72,72,71,79,64,115", "endOffsets": "256,333,425,521,603,681,764,846,924,1002,1083,1153,1236,1309,1382,1454,1534,1599,1715"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "864,947,1024,1116,1212,1294,1372,7338,7420,7498,7576,7657,7727,7810,7883,7956,8129,8209,8274", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,82,72,72,71,79,64,115", "endOffsets": "942,1019,1111,1207,1289,1367,1450,7415,7493,7571,7652,7722,7805,7878,7951,8023,8204,8269,8385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c89a420d9a9a1fa6ac16fec2566676f\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "177,270,370,467,566,662,764,8028", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "265,365,462,561,657,759,859,8124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99e5d1610f557242616bbc837e13481b\\transformed\\foundation-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,214", "endColumns": "71,86,85", "endOffsets": "122,209,295"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8390,8477", "endColumns": "71,86,85", "endOffsets": "172,8472,8558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9363d7fb6059d76c439085d85c29cdfb\\transformed\\material3-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,276,384,491,585,675,782,910,1020,1149,1231,1329,1416,1509,1619,1738,1841,1964,2089,2213,2361,2477,2590,2704,2819,2907,3002,3112,3231,3326,3428,3530,3650,3776,3880,3976,4050,4143,4235,4334,4418,4503,4605,4686,4769,4869,4966,5061,5156,5241,5343,5442,5541,5659,5740,5841", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "161,271,379,486,580,670,777,905,1015,1144,1226,1324,1411,1504,1614,1733,1836,1959,2084,2208,2356,2472,2585,2699,2814,2902,2997,3107,3226,3321,3423,3525,3645,3771,3875,3971,4045,4138,4230,4329,4413,4498,4600,4681,4764,4864,4961,5056,5151,5236,5338,5437,5536,5654,5735,5836,5933"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1455,1566,1676,1784,1891,1985,2075,2182,2310,2420,2549,2631,2729,2816,2909,3019,3138,3241,3364,3489,3613,3761,3877,3990,4104,4219,4307,4402,4512,4631,4726,4828,4930,5050,5176,5280,5376,5450,5543,5635,5734,5818,5903,6005,6086,6169,6269,6366,6461,6556,6641,6743,6842,6941,7059,7140,7241", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "1561,1671,1779,1886,1980,2070,2177,2305,2415,2544,2626,2724,2811,2904,3014,3133,3236,3359,3484,3608,3756,3872,3985,4099,4214,4302,4397,4507,4626,4721,4823,4925,5045,5171,5275,5371,5445,5538,5630,5729,5813,5898,6000,6081,6164,6264,6361,6456,6551,6636,6738,6837,6936,7054,7135,7236,7333"}}]}]}