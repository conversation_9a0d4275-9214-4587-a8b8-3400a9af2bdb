{"logs": [{"outputFile": "com.mediatek.gpu.governor.composeApp-mergeDebugResources-45:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9662c755420fe10d7c86c255f743f9b5\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "6077", "endColumns": "65", "endOffsets": "6138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99e5d1610f557242616bbc837e13481b\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "155,244,245", "startColumns": "4,4,4", "startOffsets": "7870,14016,14072", "endColumns": "45,55,54", "endOffsets": "7911,14067,14122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2deb682526b95f4bc83f31e125c3e299\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "125,128", "startColumns": "4,4", "startOffsets": "6249,6373", "endColumns": "53,66", "endOffsets": "6298,6435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3535d7f2b5d0f217ddd5a6229ba1534e\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "129,148", "startColumns": "4,4", "startOffsets": "6440,7427", "endColumns": "41,59", "endOffsets": "6477,7482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f854b63d4c8b050504f455bb1c78467\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "150", "startColumns": "4", "startOffsets": "7541", "endColumns": "49", "endOffsets": "7586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d4d642d93ef383c09cb36e083cd2a6f2\\transformed\\window-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,119,283,289,403,411,426", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,382,601,880,1194,1382,1569,1622,1682,1734,1779,5904,16372,16567,20490,20772,21386", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,119,288,293,410,425,441", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "204,377,596,815,1189,1377,1564,1617,1677,1729,1774,1813,5959,16562,16720,20767,21381,22035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1115e786e037742d4d588599683945b9\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,123,124,151,163,164,165,166,167,168,169,231,232,233,234,235,236,237,238,239,241,242,243,246,262,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3920,3979,4038,4098,4158,4218,4278,4338,4398,4458,4518,4578,4638,4697,4757,4817,4877,4937,4997,5057,5117,5177,5237,5297,5356,5416,5476,5535,5594,5653,5712,5771,5830,5964,6022,6143,6194,7591,8427,8492,8546,8612,8713,8771,8823,13324,13386,13440,13490,13544,13590,13644,13690,13732,13843,13890,13926,14127,15107,15218", "endLines": "86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,123,124,151,163,164,165,166,167,168,169,231,232,233,234,235,236,237,238,239,241,242,243,248,264,269", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "3974,4033,4093,4153,4213,4273,4333,4393,4453,4513,4573,4633,4692,4752,4812,4872,4932,4992,5052,5112,5172,5232,5292,5351,5411,5471,5530,5589,5648,5707,5766,5825,5899,6017,6072,6189,6244,7639,8487,8541,8607,8708,8766,8818,8878,13381,13435,13485,13539,13585,13639,13685,13727,13767,13885,13921,14011,14234,15213,15470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9363d7fb6059d76c439085d85c29cdfb\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "152,170,171,172,173,174,175,176,177,178,179,182,183,184,185,186,187,188,189,190,191,192,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,249,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7644,8883,8971,9057,9138,9222,9291,9356,9439,9545,9631,9751,9805,9874,9935,10004,10093,10188,10262,10359,10452,10550,10699,10790,10878,10974,11072,11136,11204,11291,11385,11452,11524,11596,11697,11806,11882,11951,11999,12065,12129,12203,12260,12317,12389,12439,12493,12564,12635,12705,12774,12832,12908,12979,13053,13139,13189,13259,14239,14954", "endLines": "152,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,189,190,191,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,258,261", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7712,8966,9052,9133,9217,9286,9351,9434,9540,9626,9746,9800,9869,9930,9999,10088,10183,10257,10354,10447,10545,10694,10785,10873,10969,11067,11131,11199,11286,11380,11447,11519,11591,11692,11801,11877,11946,11994,12060,12124,12198,12255,12312,12384,12434,12488,12559,12630,12700,12769,12827,12903,12974,13048,13134,13184,13254,13319,14949,15102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f4ecfd40a8a3d8ef234f95c6f419f9a\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "146", "startColumns": "4", "startOffsets": "7341", "endColumns": "42", "endOffsets": "7379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c89a420d9a9a1fa6ac16fec2566676f\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,126,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,153,156,157,158,159,160,161,162,240,270,271,275,276,280,281,282,294,300,310,343,364,397", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,1818,1890,1978,2043,2109,2178,2241,2311,2379,2451,2521,2582,2656,2729,2790,2851,2913,2977,3039,3100,3168,3268,3328,3394,3467,3536,3593,3645,3707,3779,3855,6303,6338,6482,6537,6600,6655,6713,6771,6832,6895,6952,7003,7053,7114,7171,7237,7271,7306,7717,7916,7983,8055,8124,8193,8267,8339,13772,15475,15592,15793,15903,16104,16233,16305,16725,16928,17229,18960,19641,20323", "endLines": "25,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,126,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,153,156,157,158,159,160,161,162,240,270,274,275,279,280,281,282,299,309,342,363,396,402", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "875,1885,1973,2038,2104,2173,2236,2306,2374,2446,2516,2577,2651,2724,2785,2846,2908,2972,3034,3095,3163,3263,3323,3389,3462,3531,3588,3640,3702,3774,3850,3915,6333,6368,6532,6595,6650,6708,6766,6827,6890,6947,6998,7048,7109,7166,7232,7266,7301,7336,7782,7978,8050,8119,8188,8262,8334,8422,13838,15587,15788,15898,16099,16228,16300,16367,16923,17224,18955,19636,20318,20485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f01b189102fff9f889914796f9dcaa28\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "149", "startColumns": "4", "startOffsets": "7487", "endColumns": "53", "endOffsets": "7536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ee8d75249f9cdd14d735a7deb2613ec\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "147", "startColumns": "4", "startOffsets": "7384", "endColumns": "42", "endOffsets": "7422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b2dafa6512813fddab8b964acaff18ec\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "154", "startColumns": "4", "startOffsets": "7787", "endColumns": "82", "endOffsets": "7865"}}]}]}