com.mediatek.gpu.governor.composeApp-ui-geometry-release-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0914735b6c9defb46bc5f35b6d2a1717\transformed\ui-geometry-release\res
com.mediatek.gpu.governor.composeApp-material3-window-size-class-release-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0def20407f9f0d10b7f2461cc6d373b8\transformed\material3-window-size-class-release\res
com.mediatek.gpu.governor.composeApp-ui-release-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1115e786e037742d4d588599683945b9\transformed\ui-release\res
com.mediatek.gpu.governor.composeApp-foundation-layout-release-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\198de8d45a36ae724695119b1b798c68\transformed\foundation-layout-release\res
com.mediatek.gpu.governor.composeApp-animation-release-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2495268cba8e279ffbda309bb552c265\transformed\animation-release\res
com.mediatek.gpu.governor.composeApp-core-1.0.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f6200c557ae83cc017f5a87353d386\transformed\core-1.0.0\res
com.mediatek.gpu.governor.composeApp-lifecycle-viewmodel-savedstate-2.8.7-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d4db9bf6098eee9c922a173d76161\transformed\lifecycle-viewmodel-savedstate-2.8.7\res
com.mediatek.gpu.governor.composeApp-core-1.13.1-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\res
com.mediatek.gpu.governor.composeApp-customview-poolingcontainer-1.0.0-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2deb682526b95f4bc83f31e125c3e299\transformed\customview-poolingcontainer-1.0.0\res
com.mediatek.gpu.governor.composeApp-lifecycle-viewmodel-release-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f854b63d4c8b050504f455bb1c78467\transformed\lifecycle-viewmodel-release\res
com.mediatek.gpu.governor.composeApp-activity-1.10.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3535d7f2b5d0f217ddd5a6229ba1534e\transformed\activity-1.10.1\res
com.mediatek.gpu.governor.composeApp-emoji2-1.4.0-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\res
com.mediatek.gpu.governor.composeApp-activity-ktx-1.10.1-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\379e848ab9eb4fd698728ea56b5b9726\transformed\activity-ktx-1.10.1\res
com.mediatek.gpu.governor.composeApp-lifecycle-viewmodel-ktx-2.8.7-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b2ca1aea95899918e7f041ef457e32b\transformed\lifecycle-viewmodel-ktx-2.8.7\res
com.mediatek.gpu.governor.composeApp-lifecycle-livedata-core-2.8.7-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40551c419a6f124729c847706ceee06d\transformed\lifecycle-livedata-core-2.8.7\res
com.mediatek.gpu.governor.composeApp-material-icons-core-release-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4445a0695f3f6d318f5ba4e2bb427fd3\transformed\material-icons-core-release\res
com.mediatek.gpu.governor.composeApp-core-runtime-2.2.0-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c26d4831c65dd39e144fdfbb31935e\transformed\core-runtime-2.2.0\res
com.mediatek.gpu.governor.composeApp-core-ktx-1.13.1-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a18c53d3367d6453984f03f8512d945\transformed\core-ktx-1.13.1\res
com.mediatek.gpu.governor.composeApp-ui-tooling-preview-release-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcc683005cf55b56fc9f62ed820a97a\transformed\ui-tooling-preview-release\res
com.mediatek.gpu.governor.composeApp-ui-util-release-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e591cb8f01d74fba819dc7958157036\transformed\ui-util-release\res
com.mediatek.gpu.governor.composeApp-ui-text-release-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\671f2384da3f5cf3eaea8880b01887e6\transformed\ui-text-release\res
com.mediatek.gpu.governor.composeApp-runtime-release-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\708ff59420332f6bb31ca51e6c9f4b9c\transformed\runtime-release\res
com.mediatek.gpu.governor.composeApp-material-ripple-release-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7296ca5c2c912a24d979fe2902e38f7a\transformed\material-ripple-release\res
com.mediatek.gpu.governor.composeApp-graphics-path-1.0.1-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7789ffe9cbdd3279f7c58ac3d6473667\transformed\graphics-path-1.0.1\res
com.mediatek.gpu.governor.composeApp-lifecycle-runtime-compose-release-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77a4eb7013d659d910cefe8b174726b8\transformed\lifecycle-runtime-compose-release\res
com.mediatek.gpu.governor.composeApp-lifecycle-runtime-release-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ee8d75249f9cdd14d735a7deb2613ec\transformed\lifecycle-runtime-release\res
com.mediatek.gpu.governor.composeApp-core-viewtree-1.0.0-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f4ecfd40a8a3d8ef234f95c6f419f9a\transformed\core-viewtree-1.0.0\res
com.mediatek.gpu.governor.composeApp-material3-release-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9363d7fb6059d76c439085d85c29cdfb\transformed\material3-release\res
com.mediatek.gpu.governor.composeApp-ui-graphics-release-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9662c755420fe10d7c86c255f743f9b5\transformed\ui-graphics-release\res
com.mediatek.gpu.governor.composeApp-foundation-release-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99e5d1610f557242616bbc837e13481b\transformed\foundation-release\res
com.mediatek.gpu.governor.composeApp-runtime-saveable-release-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05cdc28a22739b5321cb1c6c2f7e672\transformed\runtime-saveable-release\res
com.mediatek.gpu.governor.composeApp-annotation-experimental-1.4.1-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3b045978c4e02c2fd6b82f9491e4be7\transformed\annotation-experimental-1.4.1\res
com.mediatek.gpu.governor.composeApp-lifecycle-process-2.8.7-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\res
com.mediatek.gpu.governor.composeApp-startup-runtime-1.1.1-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dafa6512813fddab8b964acaff18ec\transformed\startup-runtime-1.1.1\res
com.mediatek.gpu.governor.composeApp-animation-core-release-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1e0fcb64c1626084da894b9fe517a49\transformed\animation-core-release\res
com.mediatek.gpu.governor.composeApp-graphics-shapes-release-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c56da6250c65096daeda1a5980d2d7cc\transformed\graphics-shapes-release\res
com.mediatek.gpu.governor.composeApp-window-1.3.0-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\res
com.mediatek.gpu.governor.composeApp-ui-unit-release-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e01c7c49b8b51ada9cdc729ca4a1de85\transformed\ui-unit-release\res
com.mediatek.gpu.governor.composeApp-savedstate-ktx-1.2.1-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e302f6c215566981d8ab41f3323c3673\transformed\savedstate-ktx-1.2.1\res
com.mediatek.gpu.governor.composeApp-savedstate-1.2.1-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f01b189102fff9f889914796f9dcaa28\transformed\savedstate-1.2.1\res
com.mediatek.gpu.governor.composeApp-activity-compose-1.10.1-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0684023b7b268a8ab3db7bdcbbebc49\transformed\activity-compose-1.10.1\res
com.mediatek.gpu.governor.composeApp-profileinstaller-1.4.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\res
com.mediatek.gpu.governor.composeApp-lifecycle-runtime-ktx-release-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4a9841721e73b074887eb85562bde2a\transformed\lifecycle-runtime-ktx-release\res
com.mediatek.gpu.governor.composeApp-pngs-43 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\build\generated\res\pngs\debug
com.mediatek.gpu.governor.composeApp-resValues-44 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\build\generated\res\resValues\debug
com.mediatek.gpu.governor.composeApp-packageDebugResources-45 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.mediatek.gpu.governor.composeApp-packageDebugResources-46 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.mediatek.gpu.governor.composeApp-debug-47 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\build\intermediates\merged_res\debug\mergeDebugResources
com.mediatek.gpu.governor.composeApp-androidDebug-48 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidDebug\res
com.mediatek.gpu.governor.composeApp-androidMain-49 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\res
com.mediatek.gpu.governor.composeApp-debug-50 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\debug\res
com.mediatek.gpu.governor.composeApp-main-51 D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\main\res
