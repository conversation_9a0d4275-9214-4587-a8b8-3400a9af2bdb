1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mediatek.gpu.governor"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.mediatek.gpu.governor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.mediatek.gpu.governor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:4:5-19:19
18        android:allowBackup="true"
18-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:5:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:debuggable="true"
21        android:extractNativeLibs="false"
22        android:icon="@android:drawable/ic_menu_preferences"
22-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:6:9-61
23        android:label="天玑GPU调速器"
23-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:7:9-33
24        android:theme="@android:style/Theme.DeviceDefault.NoActionBar" >
24-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:8:9-71
25        <activity
25-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:9:9-18:20
26            android:name="com.mediatek.gpu.governor.MainActivity"
26-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:10:13-41
27            android:exported="true"
27-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:11:13-36
28            android:launchMode="singleTop"
28-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:12:13-43
29            android:theme="@android:style/Theme.DeviceDefault.NoActionBar" >
29-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:13:13-75
30            <intent-filter>
30-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:14:13-17:29
31                <action android:name="android.intent.action.MAIN" />
31-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:15:17-69
31-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:15:25-66
32
33                <category android:name="android.intent.category.LAUNCHER" />
33-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:16:17-77
33-->D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:16:27-74
34            </intent-filter>
35        </activity>
36
37        <provider
37-->[org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:8:9-13:20
38            android:name="org.jetbrains.compose.resources.AndroidContextProvider"
38-->[org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:9:13-82
39            android:authorities="com.mediatek.gpu.governor.resources.AndroidContextProvider"
39-->[org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:10:13-84
40            android:enabled="true"
40-->[org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:11:13-35
41            android:exported="false" >
41-->[org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:12:13-37
42        </provider>
43        <provider
43-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
44            android:name="androidx.startup.InitializationProvider"
44-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
45            android:authorities="com.mediatek.gpu.governor.androidx-startup"
45-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
46            android:exported="false" >
46-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
47            <meta-data
47-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.emoji2.text.EmojiCompatInitializer"
48-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
49                android:value="androidx.startup" />
49-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
51-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
52                android:value="androidx.startup" />
52-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
54-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
55                android:value="androidx.startup" />
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
56        </provider>
57
58        <uses-library
58-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:23:9-25:40
59            android:name="androidx.window.extensions"
59-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:24:13-54
60            android:required="false" />
60-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:25:13-37
61        <uses-library
61-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:26:9-28:40
62            android:name="androidx.window.sidecar"
62-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:27:13-51
63            android:required="false" />
63-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:28:13-37
64
65        <receiver
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
66            android:name="androidx.profileinstaller.ProfileInstallReceiver"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
67            android:directBootAware="false"
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
68            android:enabled="true"
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
69            android:exported="true"
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
70            android:permission="android.permission.DUMP" >
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
72                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
75                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
78                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
81                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
82            </intent-filter>
83        </receiver>
84    </application>
85
86</manifest>
