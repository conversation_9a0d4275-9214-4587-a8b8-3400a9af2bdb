{"logs": [{"outputFile": "com.mediatek.gpu.governor.composeApp-mergeDebugResources-45:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1115e786e037742d4d588599683945b9\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "197,292,378,475,574,660,743,840,931,1018,1103,1193,1269,1354,1430,1509,1584,1660,1727", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "287,373,470,569,655,738,835,926,1013,1098,1188,1264,1349,1425,1504,1579,1655,1722,1835"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "931,1026,1112,1209,1308,1394,1477,7830,7921,8008,8093,8183,8259,8344,8420,8499,8675,8751,8818", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "1021,1107,1204,1303,1389,1472,1569,7916,8003,8088,8178,8254,8339,8415,8494,8569,8746,8813,8926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c89a420d9a9a1fa6ac16fec2566676f\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "196,293,395,494,594,701,811,8574", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "288,390,489,589,696,806,926,8670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9363d7fb6059d76c439085d85c29cdfb\\transformed\\material3-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4763,4852,4953,5033,5117,5218,5324,5416,5515,5603,5715,5816,5920,6039,6119,6219", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4758,4847,4948,5028,5112,5213,5319,5411,5510,5598,5710,5811,5915,6034,6114,6214,6306"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1574,1693,1814,1930,2046,2148,2245,2359,2493,2611,2763,2847,2948,3043,3143,3258,3388,3494,3633,3769,3900,4066,4193,4313,4437,4557,4653,4750,4870,4986,5086,5197,5306,5446,5591,5701,5804,5890,5984,6076,6192,6282,6371,6472,6552,6636,6737,6843,6935,7034,7122,7234,7335,7439,7558,7638,7738", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "1688,1809,1925,2041,2143,2240,2354,2488,2606,2758,2842,2943,3038,3138,3253,3383,3489,3628,3764,3895,4061,4188,4308,4432,4552,4648,4745,4865,4981,5081,5192,5301,5441,5586,5696,5799,5885,5979,6071,6187,6277,6366,6467,6547,6631,6732,6838,6930,7029,7117,7229,7330,7434,7553,7633,7733,7825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99e5d1610f557242616bbc837e13481b\\transformed\\foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,146,229", "endColumns": "90,82,84", "endOffsets": "141,224,309"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8931,9014", "endColumns": "90,82,84", "endOffsets": "191,9009,9094"}}]}]}