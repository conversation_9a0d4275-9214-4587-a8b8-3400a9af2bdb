{"logs": [{"outputFile": "com.mediatek.gpu.governor.composeApp-mergeDebugResources-45:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c89a420d9a9a1fa6ac16fec2566676f\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "178,281,383,486,591,692,794,8629", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "276,378,481,586,687,789,908,8725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1115e786e037742d4d588599683945b9\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,276,363,455,555,641,718,816,904,991,1069,1151,1221,1305,1380,1457,1533,1616,1683", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,83,74,76,75,82,66,118", "endOffsets": "271,358,450,550,636,713,811,899,986,1064,1146,1216,1300,1375,1452,1528,1611,1678,1797"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "913,1010,1097,1189,1289,1375,1452,7912,8000,8087,8165,8247,8317,8401,8476,8553,8730,8813,8880", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,83,74,76,75,82,66,118", "endOffsets": "1005,1092,1184,1284,1370,1447,1545,7995,8082,8160,8242,8312,8396,8471,8548,8624,8808,8875,8994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9363d7fb6059d76c439085d85c29cdfb\\transformed\\material3-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4763,4857,4948,5053,5133,5218,5319,5425,5518,5619,5706,5814,5913,6016,6140,6220,6323", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4758,4852,4943,5048,5128,5213,5314,5420,5513,5614,5701,5809,5908,6011,6135,6215,6318,6412"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1550,1672,1789,1902,2021,2115,2215,2332,2475,2601,2752,2837,2942,3038,3133,3249,3379,3489,3632,3770,3901,4093,4219,4348,4483,4613,4710,4806,4923,5045,5150,5255,5358,5500,5650,5757,5866,5941,6045,6147,6258,6352,6443,6548,6628,6713,6814,6920,7013,7114,7201,7309,7408,7511,7635,7715,7818", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "1667,1784,1897,2016,2110,2210,2327,2470,2596,2747,2832,2937,3033,3128,3244,3374,3484,3627,3765,3896,4088,4214,4343,4478,4608,4705,4801,4918,5040,5145,5250,5353,5495,5645,5752,5861,5936,6040,6142,6253,6347,6438,6543,6623,6708,6809,6915,7008,7109,7196,7304,7403,7506,7630,7710,7813,7907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99e5d1610f557242616bbc837e13481b\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,87", "endOffsets": "123,208,296"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8999,9084", "endColumns": "72,84,87", "endOffsets": "173,9079,9167"}}]}]}