-- Merging decision tree log ---
manifest
ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:2:1-20:12
INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:2:1-20:12
INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:2:1-20:12
INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:2:1-20:12
MERGED from [top.yukonga.miuix.kmp:miuix-android:0.4.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a678e34ea0d9119d84223046860a24e0\transformed\miuix-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3535d7f2b5d0f217ddd5a6229ba1534e\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:2:1-16:12
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0def20407f9f0d10b7f2461cc6d373b8\transformed\material3-window-size-class-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9363d7fb6059d76c439085d85c29cdfb\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4445a0695f3f6d318f5ba4e2bb427fd3\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7296ca5c2c912a24d979fe2902e38f7a\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1e0fcb64c1626084da894b9fe517a49\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2495268cba8e279ffbda309bb552c265\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\198de8d45a36ae724695119b1b798c68\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99e5d1610f557242616bbc837e13481b\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcc683005cf55b56fc9f62ed820a97a\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e01c7c49b8b51ada9cdc729ca4a1de85\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9662c755420fe10d7c86c255f743f9b5\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0914735b6c9defb46bc5f35b6d2a1717\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e591cb8f01d74fba819dc7958157036\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\671f2384da3f5cf3eaea8880b01887e6\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1115e786e037742d4d588599683945b9\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\379e848ab9eb4fd698728ea56b5b9726\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0684023b7b268a8ab3db7bdcbbebc49\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f854b63d4c8b050504f455bb1c78467\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e302f6c215566981d8ab41f3323c3673\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40551c419a6f124729c847706ceee06d\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d4db9bf6098eee9c922a173d76161\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f01b189102fff9f889914796f9dcaa28\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b2ca1aea95899918e7f041ef457e32b\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bd3e74f33a7e4ab53274444312c777a\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c56da6250c65096daeda1a5980d2d7cc\transformed\graphics-shapes-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2deb682526b95f4bc83f31e125c3e299\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a18c53d3367d6453984f03f8512d945\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7789ffe9cbdd3279f7c58ac3d6473667\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ee8d75249f9cdd14d735a7deb2613ec\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4a9841721e73b074887eb85562bde2a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77a4eb7013d659d910cefe8b174726b8\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91fc036f3a5feb9ab189822c7194dfa3\transformed\library-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05cdc28a22739b5321cb1c6c2f7e672\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\708ff59420332f6bb31ca51e6c9f4b9c\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f6200c557ae83cc017f5a87353d386\transformed\core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f4ecfd40a8a3d8ef234f95c6f419f9a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3b045978c4e02c2fd6b82f9491e4be7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c26d4831c65dd39e144fdfbb31935e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dafa6512813fddab8b964acaff18ec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f63f0fee64cf8cd74b448f44b40417b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c3097d6d179828310e9f45924a1993\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1470484e08342e944cb08f66cb6637e7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:2:11-69
application
ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:4:5-19:19
INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:4:5-19:19
MERGED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:7:5-14:19
MERGED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:7:5-14:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dafa6512813fddab8b964acaff18ec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dafa6512813fddab8b964acaff18ec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c3097d6d179828310e9f45924a1993\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c3097d6d179828310e9f45924a1993\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:7:9-33
	android:icon
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:6:9-61
	android:allowBackup
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:8:9-71
activity#com.mediatek.gpu.governor.MainActivity
ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:9:9-18:20
	android:launchMode
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:12:13-43
	android:exported
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:11:13-36
	android:theme
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:13:13-75
	android:name
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:10:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:14:13-17:29
action#android.intent.action.MAIN
ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:15:17-69
	android:name
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:15:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:16:17-77
	android:name
		ADDED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml:16:27-74
uses-sdk
INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml
INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml
MERGED from [top.yukonga.miuix.kmp:miuix-android:0.4.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a678e34ea0d9119d84223046860a24e0\transformed\miuix-release\AndroidManifest.xml:5:5-44
MERGED from [top.yukonga.miuix.kmp:miuix-android:0.4.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a678e34ea0d9119d84223046860a24e0\transformed\miuix-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3535d7f2b5d0f217ddd5a6229ba1534e\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3535d7f2b5d0f217ddd5a6229ba1534e\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0def20407f9f0d10b7f2461cc6d373b8\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0def20407f9f0d10b7f2461cc6d373b8\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9363d7fb6059d76c439085d85c29cdfb\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9363d7fb6059d76c439085d85c29cdfb\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4445a0695f3f6d318f5ba4e2bb427fd3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4445a0695f3f6d318f5ba4e2bb427fd3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7296ca5c2c912a24d979fe2902e38f7a\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7296ca5c2c912a24d979fe2902e38f7a\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1e0fcb64c1626084da894b9fe517a49\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1e0fcb64c1626084da894b9fe517a49\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2495268cba8e279ffbda309bb552c265\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2495268cba8e279ffbda309bb552c265\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\198de8d45a36ae724695119b1b798c68\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\198de8d45a36ae724695119b1b798c68\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99e5d1610f557242616bbc837e13481b\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99e5d1610f557242616bbc837e13481b\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcc683005cf55b56fc9f62ed820a97a\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcc683005cf55b56fc9f62ed820a97a\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e01c7c49b8b51ada9cdc729ca4a1de85\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e01c7c49b8b51ada9cdc729ca4a1de85\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9662c755420fe10d7c86c255f743f9b5\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9662c755420fe10d7c86c255f743f9b5\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0914735b6c9defb46bc5f35b6d2a1717\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0914735b6c9defb46bc5f35b6d2a1717\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e591cb8f01d74fba819dc7958157036\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e591cb8f01d74fba819dc7958157036\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\671f2384da3f5cf3eaea8880b01887e6\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\671f2384da3f5cf3eaea8880b01887e6\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1115e786e037742d4d588599683945b9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1115e786e037742d4d588599683945b9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\379e848ab9eb4fd698728ea56b5b9726\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\379e848ab9eb4fd698728ea56b5b9726\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0684023b7b268a8ab3db7bdcbbebc49\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0684023b7b268a8ab3db7bdcbbebc49\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f854b63d4c8b050504f455bb1c78467\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f854b63d4c8b050504f455bb1c78467\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e302f6c215566981d8ab41f3323c3673\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e302f6c215566981d8ab41f3323c3673\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40551c419a6f124729c847706ceee06d\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40551c419a6f124729c847706ceee06d\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d4db9bf6098eee9c922a173d76161\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d4db9bf6098eee9c922a173d76161\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f01b189102fff9f889914796f9dcaa28\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f01b189102fff9f889914796f9dcaa28\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b2ca1aea95899918e7f041ef457e32b\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b2ca1aea95899918e7f041ef457e32b\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bd3e74f33a7e4ab53274444312c777a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bd3e74f33a7e4ab53274444312c777a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c56da6250c65096daeda1a5980d2d7cc\transformed\graphics-shapes-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-shapes-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c56da6250c65096daeda1a5980d2d7cc\transformed\graphics-shapes-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2deb682526b95f4bc83f31e125c3e299\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2deb682526b95f4bc83f31e125c3e299\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a18c53d3367d6453984f03f8512d945\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a18c53d3367d6453984f03f8512d945\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7789ffe9cbdd3279f7c58ac3d6473667\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7789ffe9cbdd3279f7c58ac3d6473667\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ee8d75249f9cdd14d735a7deb2613ec\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ee8d75249f9cdd14d735a7deb2613ec\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4a9841721e73b074887eb85562bde2a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4a9841721e73b074887eb85562bde2a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77a4eb7013d659d910cefe8b174726b8\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77a4eb7013d659d910cefe8b174726b8\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91fc036f3a5feb9ab189822c7194dfa3\transformed\library-release\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91fc036f3a5feb9ab189822c7194dfa3\transformed\library-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05cdc28a22739b5321cb1c6c2f7e672\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05cdc28a22739b5321cb1c6c2f7e672\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\708ff59420332f6bb31ca51e6c9f4b9c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\708ff59420332f6bb31ca51e6c9f4b9c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f6200c557ae83cc017f5a87353d386\transformed\core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f6200c557ae83cc017f5a87353d386\transformed\core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f4ecfd40a8a3d8ef234f95c6f419f9a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f4ecfd40a8a3d8ef234f95c6f419f9a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3b045978c4e02c2fd6b82f9491e4be7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3b045978c4e02c2fd6b82f9491e4be7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c26d4831c65dd39e144fdfbb31935e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c26d4831c65dd39e144fdfbb31935e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dafa6512813fddab8b964acaff18ec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dafa6512813fddab8b964acaff18ec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f63f0fee64cf8cd74b448f44b40417b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f63f0fee64cf8cd74b448f44b40417b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c3097d6d179828310e9f45924a1993\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c3097d6d179828310e9f45924a1993\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1470484e08342e944cb08f66cb6637e7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1470484e08342e944cb08f66cb6637e7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\AndroidManifest.xml
provider#org.jetbrains.compose.resources.AndroidContextProvider
ADDED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:8:9-13:20
	android:enabled
		ADDED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:11:13-35
	android:authorities
		ADDED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:10:13-84
	android:exported
		ADDED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [org.jetbrains.compose.components:components-resources-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9a41251e317309d7d6fdc566f883ef\transformed\library-release\AndroidManifest.xml:9:13-82
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dafa6512813fddab8b964acaff18ec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dafa6512813fddab8b964acaff18ec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bcc9c0051fdd7a5f86970c42f30524\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3d1000e0397b84382c7468dc3629000\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4d642d93ef383c09cb36e083cd2a6f2\transformed\window-1.3.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.mediatek.gpu.governor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.mediatek.gpu.governor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c89a420d9a9a1fa6ac16fec2566676f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f314c0cc45fda138a4ceca7456f6036f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
