<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\main\assets"/><source path="D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidMain\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\debug\assets"/><source path="D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\src\androidDebug\assets"/></dataSet><dataSet config="assets-debugAssetsCopyForAGP" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\build\generated\assets\debugAssetsCopyForAGP"/></dataSet><dataSet config="assets-copyDebugComposeResourcesToAndroidAssets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\build\generated\assets\copyDebugComposeResourcesToAndroidAssets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Code\src\APP\Mediatek_Mali_GPU_Governor_UI\composeApp\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>