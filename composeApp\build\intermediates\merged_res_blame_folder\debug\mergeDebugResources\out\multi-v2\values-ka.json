{"logs": [{"outputFile": "com.mediatek.gpu.governor.composeApp-mergeDebugResources-45:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99e5d1610f557242616bbc837e13481b\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,228", "endColumns": "84,87,90", "endOffsets": "135,223,314"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8949,9037", "endColumns": "84,87,90", "endOffsets": "185,9032,9123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9363d7fb6059d76c439085d85c29cdfb\\transformed\\material3-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,411,525,625,724,840,976,1094,1242,1328,1430,1524,1622,1744,1864,1971,2106,2243,2378,2550,2679,2796,2914,3035,3130,3227,3345,3484,3587,3689,3800,3938,4078,4189,4292,4369,4464,4562,4672,4758,4845,4958,5038,5123,5224,5327,5421,5523,5609,5715,5811,5919,6036,6116,6222", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "168,285,406,520,620,719,835,971,1089,1237,1323,1425,1519,1617,1739,1859,1966,2101,2238,2373,2545,2674,2791,2909,3030,3125,3222,3340,3479,3582,3684,3795,3933,4073,4184,4287,4364,4459,4557,4667,4753,4840,4953,5033,5118,5219,5322,5416,5518,5604,5710,5806,5914,6031,6111,6217,6314"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1564,1682,1799,1920,2034,2134,2233,2349,2485,2603,2751,2837,2939,3033,3131,3253,3373,3480,3615,3752,3887,4059,4188,4305,4423,4544,4639,4736,4854,4993,5096,5198,5309,5447,5587,5698,5801,5878,5973,6071,6181,6267,6354,6467,6547,6632,6733,6836,6930,7032,7118,7224,7320,7428,7545,7625,7731", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "1677,1794,1915,2029,2129,2228,2344,2480,2598,2746,2832,2934,3028,3126,3248,3368,3475,3610,3747,3882,4054,4183,4300,4418,4539,4634,4731,4849,4988,5091,5193,5304,5442,5582,5693,5796,5873,5968,6066,6176,6262,6349,6462,6542,6627,6728,6831,6925,7027,7113,7219,7315,7423,7540,7620,7726,7823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c89a420d9a9a1fa6ac16fec2566676f\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "190,286,388,487,586,692,796,8578", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "281,383,482,581,687,791,909,8674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1115e786e037742d4d588599683945b9\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,287,373,472,575,665,745,841,931,1018,1107,1198,1270,1360,1438,1516,1591,1670,1740", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "282,368,467,570,660,740,836,926,1013,1102,1193,1265,1355,1433,1511,1586,1665,1735,1856"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "914,1010,1096,1195,1298,1388,1468,7828,7918,8005,8094,8185,8257,8347,8425,8503,8679,8758,8828", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "1005,1091,1190,1293,1383,1463,1559,7913,8000,8089,8180,8252,8342,8420,8498,8573,8753,8823,8944"}}]}]}